// Modern JavaScript Application
console.log('🚀 JobSearch App JavaScript Loading...');

class JobSearchApp {
    constructor() {
        console.log('🏗️ JobSearchApp constructor called');
        this.currentCompany = null;
        this.foundContacts = [];
        this.selectedRecipients = [];
        this.manualEmails = [];
        this.init();
    }

    init() {
        this.bindEvents();
        this.loadInitialData();
        this.initQuickActions();
        this.debugElementsExistence();
    }

    // Event Bindings
    bindEvents() {
        // Company Search Form
        document.getElementById('companySearchForm').addEventListener('submit', (e) => {
            this.handleCompanySearch(e);
        });

        // Contact Search Button
        document.getElementById('searchContactsBtn').addEventListener('click', () => {
            this.handleContactSearch();
        });

        // SMTP Configuration Form
        document.getElementById('smtpConfigForm').addEventListener('submit', (e) => {
            this.handleSMTPConfig(e);
        });

        // Email Template Form
        document.getElementById('emailTemplateForm').addEventListener('submit', (e) => {
            this.handleEmailTemplate(e);
        });

        // Send Emails Button
        document.getElementById('sendEmailsBtn').addEventListener('click', () => {
            this.handleSendEmails();
        });

        // Bulk Operation Form
        document.getElementById('bulkOperationForm').addEventListener('submit', (e) => {
            this.handleBulkOperation(e);
        });

        // Template Selection Handler
        document.getElementById('emailTemplateSelect').addEventListener('change', (e) => {
            this.handleTemplateSelection(e);
        });

        // Manual Email Entry Handler - use event delegation for better reliability
        this.bindManualEmailEvents();
    }

    // Debug function to check element existence
    debugElementsExistence() {
        console.log('JobSearchApp initialized');

        // Check immediately
        this.checkElementsNow();

        // Check after DOM is fully loaded
        setTimeout(() => {
            console.log('Checking elements after 1 second...');
            this.checkElementsNow();
        }, 1000);

        // Check after 3 seconds (in case tabs are switched)
        setTimeout(() => {
            console.log('Checking elements after 3 seconds...');
            this.checkElementsNow();
        }, 3000);
    }

    checkElementsNow() {
        const emailInput = document.getElementById('manualEmailAddress');
        const nameInput = document.getElementById('manualContactName');
        const addBtn = document.getElementById('addManualEmailBtn');

        console.log('Elements existence check:');
        console.log('Email input:', emailInput ? 'Found' : 'NOT FOUND');
        console.log('Name input:', nameInput ? 'Found' : 'NOT FOUND');
        console.log('Add button:', addBtn ? 'Found' : 'NOT FOUND');

        if (emailInput && nameInput && addBtn) {
            console.log('✅ All manual email elements found!');
        } else {
            console.log('❌ Some manual email elements missing');
        }
    }

    // Manual Email Events - with better error handling
    bindManualEmailEvents() {
        console.log('Binding manual email events...');

        // Use event delegation to handle elements that might not exist yet
        document.addEventListener('click', (e) => {
            if (e.target && e.target.id === 'addManualEmailBtn') {
                e.preventDefault();
                console.log('🔥 Add manual email button clicked!'); // Debug log
                this.handleAddManualEmail();
            }

            // Handle test button click
            if (e.target && e.target.id === 'testManualEmailBtn') {
                e.preventDefault();
                console.log('🧪 Test button clicked!');
                if (typeof window.testManualEmail === 'function') {
                    window.testManualEmail();
                } else {
                    console.error('❌ testManualEmail function not found');
                    alert('Test function not available. Check console for errors.');
                }
            }
        });

        document.addEventListener('keypress', (e) => {
            if (e.target && e.target.id === 'manualEmailAddress' && e.key === 'Enter') {
                e.preventDefault();
                console.log('🔥 Enter key pressed in email input!'); // Debug log
                this.handleAddManualEmail();
            }
        });

        // Also try direct binding after a delay
        setTimeout(() => {
            this.bindDirectEvents();
        }, 2000);
    }

    bindDirectEvents() {
        console.log('Attempting direct event binding...');

        const addBtn = document.getElementById('addManualEmailBtn');
        const emailInput = document.getElementById('manualEmailAddress');

        if (addBtn) {
            console.log('✅ Found add button, binding click event');
            addBtn.addEventListener('click', (e) => {
                e.preventDefault();
                console.log('🎯 Direct click event fired!');
                this.handleAddManualEmail();
            });
        } else {
            console.log('❌ Add button not found for direct binding');
        }

        if (emailInput) {
            console.log('✅ Found email input, binding keypress event');
            emailInput.addEventListener('keypress', (e) => {
                if (e.key === 'Enter') {
                    e.preventDefault();
                    console.log('🎯 Direct keypress event fired!');
                    this.handleAddManualEmail();
                }
            });
        } else {
            console.log('❌ Email input not found for direct binding');
        }
    }

    // Quick Actions
    initQuickActions() {
        document.querySelectorAll('.quick-action-item').forEach(item => {
            item.addEventListener('click', (e) => {
                const action = e.currentTarget.dataset.action;
                this.handleQuickAction(action);
            });
        });
    }

    handleQuickAction(action) {
        const tabs = {
            'search': 'search-tab',
            'contacts': 'search-tab',
            'email': 'email-tab'
        };

        if (tabs[action]) {
            const tabElement = document.getElementById(tabs[action]);
            if (tabElement) {
                const tab = new bootstrap.Tab(tabElement);
                tab.show();
            }
        }
    }

    // Load Initial Data
    async loadInitialData() {
        await Promise.all([
            this.loadSMTPConfigs(),
            this.loadEmailTemplates()
        ]);
    }

    // Company Search Handler
    async handleCompanySearch(e) {
        e.preventDefault();
        const companyName = document.getElementById('companyName').value.trim();

        if (!companyName) {
            this.showToast('Please enter a company name', 'error');
            return;
        }

        const button = e.target.querySelector('button[type="submit"]');
        const loading = button.querySelector('.loading');

        this.setLoading(button, loading, true);

        try {
            const formData = new FormData();
            formData.append('company_name', companyName);

            // Get CSRF token
            const csrfToken = document.querySelector('meta[name=csrf-token]')?.getAttribute('content');

            const response = await fetch('/search_company', {
                method: 'POST',
                body: formData,
                headers: {
                    'X-CSRFToken': csrfToken
                }
            });

            const data = await response.json();

            if (data.error) {
                throw new Error(data.error);
            }

            this.currentCompany = data;
            this.displayCompanyResults(data, companyName);
            document.getElementById('searchContactsBtn').disabled = false;
            this.showToast('Company information found successfully!', 'success');

        } catch (error) {
            this.showToast(`Error: ${error.message}`, 'error');
        } finally {
            this.setLoading(button, loading, false);
        }
    }

    // Contact Search Handler
    async handleContactSearch() {
        if (!this.currentCompany) {
            this.showToast('Please search for a company first', 'error');
            return;
        }

        const button = document.getElementById('searchContactsBtn');
        const loading = button.querySelector('.loading');

        this.setLoading(button, loading, true);

        try {
            const formData = new FormData();
            formData.append('company_id', this.currentCompany.company_id);
            formData.append('company_name', document.getElementById('companyName').value);

            // Get CSRF token
            const csrfToken = document.querySelector('meta[name=csrf-token]')?.getAttribute('content');

            const response = await fetch('/search_contacts', {
                method: 'POST',
                body: formData,
                headers: {
                    'X-CSRFToken': csrfToken
                }
            });

            const data = await response.json();

            if (data.error) {
                throw new Error(data.error);
            }

            this.foundContacts = data.contacts;
            this.displayContactResults(data.contacts);
            this.displayEmailGeneration(data.contacts);
            this.showToast(`Found ${data.contacts.length} HR contacts!`, 'success');

        } catch (error) {
            this.showToast(`Error: ${error.message}`, 'error');
        } finally {
            this.setLoading(button, loading, false);
        }
    }

    // SMTP Configuration Handler
    async handleSMTPConfig(e) {
        e.preventDefault();

        const config = {
            name: document.getElementById('configName').value,
            smtp_server: document.getElementById('smtpServer').value,
            smtp_port: parseInt(document.getElementById('smtpPort').value),
            email: document.getElementById('smtpEmail').value,
            password: document.getElementById('smtpPassword').value,
            use_tls: document.getElementById('useTLS').checked
        };

        try {
            // Get CSRF token
            const csrfToken = document.querySelector('meta[name=csrf-token]')?.getAttribute('content');

            const response = await fetch('/add_smtp_config', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRFToken': csrfToken
                },
                body: JSON.stringify(config)
            });

            const data = await response.json();

            if (data.error) {
                throw new Error(data.error);
            }

            this.showToast('SMTP configuration saved successfully!', 'success');
            e.target.reset();
            await this.loadSMTPConfigs();

        } catch (error) {
            this.showToast(`Error: ${error.message}`, 'error');
        }
    }

    // Email Template Handler
    async handleEmailTemplate(e) {
        e.preventDefault();

        const template = {
            name: document.getElementById('templateName').value,
            subject: document.getElementById('emailSubject').value,
            body: document.getElementById('emailBody').value
        };

        try {
            // Get CSRF token
            const csrfToken = document.querySelector('meta[name=csrf-token]')?.getAttribute('content');

            const response = await fetch('/add_email_template', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRFToken': csrfToken
                },
                body: JSON.stringify(template)
            });

            const data = await response.json();

            if (data.error) {
                throw new Error(data.error);
            }

            this.showToast('Email template saved successfully!', 'success');
            e.target.reset();
            await this.loadEmailTemplates();

        } catch (error) {
            this.showToast(`Error: ${error.message}`, 'error');
        }
    }

    // Manual Email Handler
    handleAddManualEmail() {
        console.log('🚀 handleAddManualEmail called'); // Debug log

        try {
            const emailInput = document.getElementById('manualEmailAddress');
            const nameInput = document.getElementById('manualContactName');

            console.log('Elements found:', {
                emailInput: !!emailInput,
                nameInput: !!nameInput
            });

            if (!emailInput) {
                console.error('❌ Email input element not found');
                this.showToast('Email input not found. Please make sure you are on the Email Management tab.', 'error');
                return;
            }

            if (!nameInput) {
                console.error('❌ Name input element not found');
                this.showToast('Name input not found. Please make sure you are on the Email Management tab.', 'error');
                return;
            }

            const email = emailInput.value.trim();
            const name = nameInput.value.trim() || 'Manual Contact';

            console.log('📝 Input values:', { email, name });

            if (!email) {
                console.log('⚠️ No email entered');
                this.showToast('Please enter an email address', 'error');
                return;
            }

            // Basic email validation
            const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
            if (!emailRegex.test(email)) {
                console.log('⚠️ Invalid email format');
                this.showToast('Please enter a valid email address', 'error');
                return;
            }

            // Initialize arrays if they don't exist
            if (!this.manualEmails) {
                console.log('🔧 Initializing manualEmails array');
                this.manualEmails = [];
            }
            if (!this.selectedRecipients) {
                console.log('🔧 Initializing selectedRecipients array');
                this.selectedRecipients = [];
            }

            // Check if email already exists in manual emails or selected recipients
            const existsInManual = this.manualEmails.find(e => e.email === email);
            const existsInSelected = this.selectedRecipients.find(e => e.email === email);

            if (existsInManual || existsInSelected) {
                console.log('⚠️ Email already exists');
                this.showToast('This email address is already added', 'error');
                return;
            }

            // Add to manual emails
            const manualEmail = {
                email: email,
                name: name,
                title: 'Manual Entry',
                source: 'manual'
            };

            this.manualEmails.push(manualEmail);
            this.selectedRecipients.push(manualEmail);

            console.log('✅ Added manual email:', manualEmail);
            console.log('📊 Total recipients:', this.selectedRecipients.length);

            // Clear inputs
            emailInput.value = '';
            nameInput.value = '';

            // Update displays
            this.updateManualEmailList();
            this.updateSelectedRecipients();

            this.showToast(`${name} (${email}) added successfully`, 'success');

        } catch (error) {
            console.error('💥 Error in handleAddManualEmail:', error);
            this.showToast('An error occurred while adding the email. Check console for details.', 'error');
        }
    }

    // Send Emails Handler
    async handleSendEmails() {
        const smtpConfigId = document.getElementById('sendSmtpConfig').value;
        const templateId = document.getElementById('sendEmailTemplate').value;

        if (!smtpConfigId || !templateId) {
            this.showToast('Please select both SMTP configuration and email template', 'error');
            return;
        }

        if (this.selectedRecipients.length === 0) {
            this.showToast('No recipients selected. Add contacts from search or enter emails manually.', 'error');
            return;
        }

        const button = document.getElementById('sendEmailsBtn');
        const loading = button.querySelector('.loading');

        this.setLoading(button, loading, true);

        try {
            const data = {
                smtp_config_id: parseInt(smtpConfigId),
                template_id: parseInt(templateId),
                recipients: this.selectedRecipients
            };

            // Get CSRF token
            const csrfToken = document.querySelector('meta[name=csrf-token]')?.getAttribute('content');

            const response = await fetch('/send_emails', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRFToken': csrfToken
                },
                body: JSON.stringify(data)
            });

            const result = await response.json();

            if (result.error) {
                throw new Error(result.error);
            }

            this.displayEmailSendResults(result.results);
            this.showToast('Emails sent successfully!', 'success');

        } catch (error) {
            this.showToast(`Error: ${error.message}`, 'error');
        } finally {
            this.setLoading(button, loading, false);
        }
    }

    // Bulk Operation Handler
    async handleBulkOperation(e) {
        e.preventDefault();

        const companyName = document.getElementById('bulkCompanyName').value.trim();
        const smtpConfigId = document.getElementById('bulkSmtpConfig').value;
        const templateId = document.getElementById('bulkEmailTemplate').value;

        if (!companyName) {
            this.showToast('Please enter a company name', 'error');
            return;
        }

        const button = e.target.querySelector('button[type="submit"]');
        const loading = button.querySelector('.loading');
        
        this.setLoading(button, loading, true);

        try {
            const data = {
                company_name: companyName,
                smtp_config_id: smtpConfigId ? parseInt(smtpConfigId) : null,
                template_id: templateId ? parseInt(templateId) : null
            };

            // Get CSRF token
            const csrfToken = document.querySelector('meta[name=csrf-token]')?.getAttribute('content');

            const response = await fetch('/bulk_search_and_email', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRFToken': csrfToken
                },
                body: JSON.stringify(data)
            });

            const result = await response.json();

            if (result.error) {
                throw new Error(result.error);
            }

            this.displayBulkResults(result);
            this.showToast('Bulk operation completed successfully!', 'success');

        } catch (error) {
            this.showToast(`Error: ${error.message}`, 'error');
        } finally {
            this.setLoading(button, loading, false);
        }
    }

    // Template Selection Handler
    async handleTemplateSelection(e) {
        if (e.target.value) {
            try {
                const response = await fetch('/email_templates');
                const templates = await response.json();
                const template = templates.find(t => t.id == e.target.value);
                
                if (template) {
                    document.getElementById('templateName').value = template.name;
                    document.getElementById('emailSubject').value = template.subject;
                    document.getElementById('emailBody').value = template.body;
                }
            } catch (error) {
                console.error('Error loading template:', error);
            }
        }
    }

    // Display Functions
    displayCompanyResults(data, companyName) {
        const resultsDiv = document.getElementById('companyResults');
        const infoDiv = document.getElementById('companyInfo');

        infoDiv.innerHTML = `
            <div class="company-info-card">
                <div class="row">
                    <div class="col-md-4">
                        <div class="info-item">
                            <i class="bi bi-building text-primary"></i>
                            <div>
                                <div class="info-label">Company</div>
                                <div class="info-value">${companyName}</div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="info-item">
                            <i class="bi bi-globe text-success"></i>
                            <div>
                                <div class="info-label">Domain</div>
                                <div class="info-value">${data.domain || 'Not found'}</div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="info-item">
                            <i class="bi bi-link-45deg text-info"></i>
                            <div>
                                <div class="info-label">Website</div>
                                <div class="info-value">
                                    ${data.website ? `<a href="${data.website}" target="_blank" class="text-decoration-none">${data.website}</a>` : 'Not found'}
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;

        resultsDiv.style.display = 'block';
        resultsDiv.classList.add('fade-in');
    }

    displayContactResults(contacts) {
        console.log('📋 displayContactResults called with contacts:', contacts);
        const resultsDiv = document.getElementById('contactResults');
        const listDiv = document.getElementById('contactsList');

        let html = '';
        contacts.forEach((contact, index) => {
            console.log(`👤 Processing contact ${index}:`, contact);
            html += `
                <div class="contact-card">
                    <div class="contact-header">
                        <div class="contact-info">
                            <h6>${contact.name}</h6>
                            <div class="title">${contact.title}</div>
                        </div>
                        <button class="btn btn-sm btn-outline-primary" onclick="if(window.app) window.app.addToRecipients(${index}); else alert('App not ready yet');">
                            <i class="bi bi-person-plus me-1"></i>Add to Recipients
                        </button>
                    </div>
                    
                    ${contact.linkedin_url ? `
                        <div class="mb-2">
                            <small class="text-muted">
                                <i class="bi bi-linkedin me-1"></i>
                                <a href="${contact.linkedin_url}" target="_blank" class="text-decoration-none">LinkedIn Profile</a>
                            </small>
                        </div>
                    ` : ''}
                    
                    <div class="email-list">
                        <strong class="d-block mb-2">Generated Email Addresses:</strong>
                        ${contact.emails.map(email => `
                            <div class="email-item">
                                <span class="email-address">${email}</span>
                                <button class="btn btn-sm btn-outline-secondary" onclick="if(window.app) window.app.validateSingleEmail('${email}', '${contact.name}'); else alert('App not ready yet');">
                                    <i class="bi bi-check-circle me-1"></i>Validate
                                </button>
                            </div>
                        `).join('')}
                    </div>
                </div>
            `;
        });

        listDiv.innerHTML = html;
        resultsDiv.style.display = 'block';
        resultsDiv.classList.add('slide-up');
    }

    displayEmailGeneration(contacts) {
        const section = document.getElementById('emailValidationSection');
        section.style.display = 'block';
        section.classList.add('fade-in');
    }

    displayEmailSendResults(results) {
        const resultsDiv = document.getElementById('emailSendResults');
        const listDiv = document.getElementById('sendResultsList');

        let html = '<div class="row g-3">';
        results.forEach(result => {
            const statusClass = result.success ? 'success' : 'danger';
            const statusIcon = result.success ? 'check-circle' : 'x-circle';
            
            html += `
                <div class="col-md-6">
                    <div class="result-item border rounded p-3">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <div class="fw-semibold">${result.email}</div>
                                <small class="text-muted">${result.name || 'Unknown'}</small>
                            </div>
                            <span class="badge bg-${statusClass}">
                                <i class="bi bi-${statusIcon} me-1"></i>
                                ${result.success ? 'Sent' : 'Failed'}
                            </span>
                        </div>
                        ${!result.success ? `<div class="text-danger small mt-2">${result.message}</div>` : ''}
                    </div>
                </div>
            `;
        });
        html += '</div>';

        listDiv.innerHTML = html;
        resultsDiv.style.display = 'block';
        resultsDiv.classList.add('fade-in');
    }

    displayBulkResults(result) {
        const resultsDiv = document.getElementById('bulkResults');
        const contentDiv = document.getElementById('bulkResultsContent');

        let html = `
            <div class="bulk-summary mb-4">
                <div class="row g-3">
                    <div class="col-md-3">
                        <div class="stat-card text-center">
                            <div class="stat-number text-primary">${result.contacts_found}</div>
                            <div class="stat-label">Contacts Found</div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="stat-card text-center">
                            <div class="stat-number text-success">${result.total_emails_collected}</div>
                            <div class="stat-label">Emails Generated</div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="stat-card text-center">
                            <div class="stat-number text-info">${result.emails_sent || 0}</div>
                            <div class="stat-label">Emails Sent</div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="stat-card text-center">
                            <div class="stat-number text-warning">${result.company_info.domain ? '✓' : '✗'}</div>
                            <div class="stat-label">Domain Found</div>
                        </div>
                    </div>
                </div>
            </div>
        `;

        if (result.contacts && result.contacts.length > 0) {
            html += `
                <div class="contacts-preview">
                    <h6 class="fw-semibold mb-3">Found Contacts:</h6>
                    <div class="row g-2">
                        ${result.contacts.slice(0, 6).map(contact => `
                            <div class="col-md-4">
                                <div class="contact-preview">
                                    <div class="fw-semibold">${contact.name}</div>
                                    <div class="text-muted small">${contact.title}</div>
                                    <div class="small">${contact.emails.length} emails generated</div>
                                </div>
                            </div>
                        `).join('')}
                    </div>
                    ${result.contacts.length > 6 ? `<div class="text-muted small mt-2">+${result.contacts.length - 6} more contacts found</div>` : ''}
                </div>
            `;
        }

        contentDiv.innerHTML = html;
        resultsDiv.style.display = 'block';
        resultsDiv.classList.add('fade-in');
    }

    // Recipient Management
    addToRecipients(contactIndex) {
        console.log('🎯 addToRecipients called with index:', contactIndex);
        console.log('📋 foundContacts array:', this.foundContacts);

        const contact = this.foundContacts[contactIndex];
        console.log('👤 Selected contact:', contact);

        if (!contact) {
            console.log('❌ No contact found at index', contactIndex);
            this.showToast('Contact not found', 'error');
            return;
        }

        if (!contact.emails) {
            console.log('❌ Contact has no emails property:', contact);
            this.showToast('No emails property found for this contact', 'error');
            return;
        }

        if (contact.emails.length === 0) {
            console.log('❌ Contact has empty emails array:', contact.emails);
            this.showToast('No emails available for this contact', 'error');
            return;
        }

        console.log('✅ Contact has emails, showing modal:', contact.emails);
        this.showEmailSelectionModal(contact);
    }

    showEmailSelectionModal(contact) {
        const modalHtml = `
            <div class="modal fade" id="emailSelectionModal" tabindex="-1">
                <div class="modal-dialog modal-lg">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title">
                                <i class="bi bi-envelope me-2"></i>
                                Select Email for ${contact.name}
                            </h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                        </div>
                        <div class="modal-body">
                            <div class="contact-info-modal mb-3">
                                <div class="d-flex align-items-center">
                                    <div class="avatar bg-primary bg-gradient rounded-circle me-3 d-flex align-items-center justify-content-center">
                                        <i class="bi bi-person text-white"></i>
                                    </div>
                                    <div>
                                        <div class="fw-semibold">${contact.name}</div>
                                        <div class="text-muted">${contact.title}</div>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="email-selection">
                                <label class="form-label fw-semibold">Choose email address:</label>
                                ${contact.emails.map((email, i) => `
                                    <div class="form-check email-option">
                                        <input class="form-check-input" type="radio" name="selectedEmail" id="email${i}" value="${email}">
                                        <label class="form-check-label d-flex justify-content-between align-items-center" for="email${i}">
                                            <span class="email-address">${email}</span>
                                            <button type="button" class="btn btn-sm btn-outline-secondary validate-btn"
                                                    onclick="if(window.app) window.app.validateSingleEmailInModal('${email}', ${i}); else alert('App not ready yet');">
                                                <i class="bi bi-check-circle me-1"></i>Validate
                                            </button>
                                        </label>
                                        <div id="validation${i}" class="validation-result"></div>
                                    </div>
                                `).join('')}
                            </div>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                            <button type="button" class="btn btn-primary" onclick="if(window.app) window.app.confirmEmailSelection('${contact.name}', '${contact.title}'); else alert('App not ready yet');">
                                <i class="bi bi-person-plus me-1"></i>Add to Recipients
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        `;

        // Remove existing modal if any
        const existingModal = document.getElementById('emailSelectionModal');
        if (existingModal) {
            existingModal.remove();
        }

        document.body.insertAdjacentHTML('beforeend', modalHtml);
        const modal = new bootstrap.Modal(document.getElementById('emailSelectionModal'));
        modal.show();
    }

    async validateSingleEmail(email, contactName) {
        this.showToast(`Validating email: ${email}`, 'info');
        // Email validation logic would go here
        // For now, we'll just show a success message
        setTimeout(() => {
            this.showToast(`Email validation completed for ${email}`, 'success');
        }, 1000);
    }

    async validateSingleEmailInModal(email, index) {
        const validationDiv = document.getElementById(`validation${index}`);
        validationDiv.innerHTML = '<small class="text-info"><i class="bi bi-hourglass-split me-1"></i>Validating...</small>';
        
        setTimeout(() => {
            const isValid = Math.random() > 0.3; // Simulate validation
            validationDiv.innerHTML = `
                <small class="text-${isValid ? 'success' : 'danger'}">
                    <i class="bi bi-${isValid ? 'check' : 'x'}-circle me-1"></i>
                    ${isValid ? 'Valid' : 'Invalid'}
                </small>
            `;
        }, 1500);
    }

    confirmEmailSelection(name, title) {
        console.log('✅ confirmEmailSelection called with:', { name, title });

        const selectedEmail = document.querySelector('input[name="selectedEmail"]:checked');
        console.log('📧 Selected email element:', selectedEmail);

        if (!selectedEmail) {
            console.log('❌ No email selected');
            this.showToast('Please select an email address', 'error');
            return;
        }

        const recipient = {
            name: name,
            title: title,
            email: selectedEmail.value
        };

        console.log('👤 New recipient object:', recipient);
        console.log('📋 Current selectedRecipients:', this.selectedRecipients);

        // Check if recipient already exists
        const exists = this.selectedRecipients.find(r => r.email === recipient.email);
        if (exists) {
            console.log('⚠️ Recipient already exists:', exists);
            this.showToast('This recipient is already added', 'error');
            return;
        }

        this.selectedRecipients.push(recipient);
        console.log('✅ Added recipient, new array:', this.selectedRecipients);

        this.updateSelectedRecipients();
        this.showToast(`${name} added to recipients`, 'success');

        const modal = bootstrap.Modal.getInstance(document.getElementById('emailSelectionModal'));
        if (modal) {
            modal.hide();
        } else {
            console.log('⚠️ Modal instance not found, trying to remove manually');
            const modalElement = document.getElementById('emailSelectionModal');
            if (modalElement) {
                modalElement.remove();
            }
        }
    }

    updateSelectedRecipients() {
        console.log('🔄 updateSelectedRecipients called');
        const recipientsDiv = document.getElementById('selectedRecipients');
        console.log('📍 Recipients div element:', recipientsDiv);
        console.log('📊 Current selectedRecipients count:', this.selectedRecipients.length);

        if (!recipientsDiv) {
            console.log('❌ Recipients div not found!');
            return;
        }

        if (this.selectedRecipients.length === 0) {
            console.log('📭 No recipients, showing empty state');
            recipientsDiv.innerHTML = `
                <div class="empty-state">
                    <i class="bi bi-inbox"></i>
                    <p>No recipients selected</p>
                    <small>Go to Company & Contacts tab to find contacts or add emails manually</small>
                </div>
            `;
            return;
        }

        console.log('📝 Building HTML for recipients');
        let html = '';
        this.selectedRecipients.forEach((recipient, index) => {
            console.log(`👤 Processing recipient ${index}:`, recipient);
            const sourceIcon = recipient.source === 'manual' ? 'pencil-square' : 'search';
            const sourceColor = recipient.source === 'manual' ? 'success' : 'primary';

            html += `
                <div class="recipient-item">
                    <div class="recipient-info">
                        <div class="recipient-name">
                            ${recipient.name}
                            <i class="bi bi-${sourceIcon} text-${sourceColor} ms-1" title="${recipient.source === 'manual' ? 'Manual Entry' : 'Found Contact'}"></i>
                        </div>
                        <div class="text-muted small">${recipient.title}</div>
                        <div class="recipient-email">${recipient.email}</div>
                    </div>
                    <button class="btn btn-sm btn-outline-danger" onclick="if(window.app) window.app.removeRecipient(${index}); else alert('App not ready yet');">
                        <i class="bi bi-trash"></i>
                    </button>
                </div>
            `;
        });

        console.log('🎨 Generated HTML:', html);
        recipientsDiv.innerHTML = html;
        console.log('✅ Recipients div updated successfully');
    }

    updateManualEmailList() {
        const manualListDiv = document.getElementById('manualEmailList');

        if (this.manualEmails.length === 0) {
            manualListDiv.innerHTML = '';
            return;
        }

        let html = '<div class="manual-emails-header"><small class="text-muted fw-semibold">Added Emails:</small></div>';
        this.manualEmails.forEach((email, index) => {
            html += `
                <div class="manual-email-item">
                    <div class="manual-email-info">
                        <div class="manual-email-name">${email.name}</div>
                        <div class="manual-email-address">${email.email}</div>
                    </div>
                    <button class="btn btn-sm btn-outline-danger" onclick="if(window.app) window.app.removeManualEmail(${index}); else alert('App not ready yet');">
                        <i class="bi bi-x"></i>
                    </button>
                </div>
            `;
        });

        manualListDiv.innerHTML = html;
    }

    removeRecipient(index) {
        const removed = this.selectedRecipients.splice(index, 1)[0];

        // If it was a manual email, also remove from manual emails array
        if (removed.source === 'manual') {
            const manualIndex = this.manualEmails.findIndex(e => e.email === removed.email);
            if (manualIndex !== -1) {
                this.manualEmails.splice(manualIndex, 1);
                this.updateManualEmailList();
            }
        }

        this.updateSelectedRecipients();
        this.showToast(`${removed.name} removed from recipients`, 'info');
    }

    removeManualEmail(index) {
        const removed = this.manualEmails.splice(index, 1)[0];

        // Also remove from selected recipients
        const recipientIndex = this.selectedRecipients.findIndex(r => r.email === removed.email);
        if (recipientIndex !== -1) {
            this.selectedRecipients.splice(recipientIndex, 1);
            this.updateSelectedRecipients();
        }

        this.updateManualEmailList();
        this.showToast(`${removed.name} removed`, 'info');
    }

    // Data Loading Functions
    async loadSMTPConfigs() {
        try {
            const response = await fetch('/smtp_configs');
            const configs = await response.json();
            
            const selects = [
                document.getElementById('smtpConfigSelect'),
                document.getElementById('sendSmtpConfig'),
                document.getElementById('bulkSmtpConfig')
            ];

            selects.forEach(select => {
                if (select) {
                    const defaultOption = select.id === 'bulkSmtpConfig' ? 
                        '<option value="">Skip Email Sending</option>' : 
                        '<option value="">Select SMTP Configuration</option>';
                    
                    select.innerHTML = defaultOption;
                    configs.forEach(config => {
                        select.innerHTML += `<option value="${config.id}">${config.name} (${config.email})</option>`;
                    });
                }
            });
        } catch (error) {
            console.error('Error loading SMTP configs:', error);
        }
    }

    async loadEmailTemplates() {
        try {
            const response = await fetch('/email_templates');
            const templates = await response.json();
            
            const selects = [
                document.getElementById('emailTemplateSelect'),
                document.getElementById('sendEmailTemplate'),
                document.getElementById('bulkEmailTemplate')
            ];

            selects.forEach(select => {
                if (select) {
                    const defaultOption = select.id === 'bulkEmailTemplate' ? 
                        '<option value="">Skip Email Sending</option>' : 
                        '<option value="">Select Email Template</option>';
                    
                    select.innerHTML = defaultOption;
                    templates.forEach(template => {
                        select.innerHTML += `<option value="${template.id}">${template.name}</option>`;
                    });
                }
            });
        } catch (error) {
            console.error('Error loading email templates:', error);
        }
    }

    // Utility Functions
    setLoading(button, loading, isLoading) {
        if (button) {
            button.disabled = isLoading;
        }
        if (loading) {
            if (isLoading) {
                loading.classList.remove('d-none');
                loading.classList.add('show');
            } else {
                loading.classList.add('d-none');
                loading.classList.remove('show');
            }
        }
    }

    showToast(message, type = 'info') {
        let toastId;
        switch(type) {
            case 'error':
                toastId = 'errorToast';
                break;
            case 'success':
                toastId = 'successToast';
                break;
            case 'info':
            default:
                toastId = 'infoToast';
                break;
        }

        const toastElement = document.getElementById(toastId);
        if (!toastElement) {
            console.error(`Toast element ${toastId} not found`);
            return;
        }

        const toastBody = toastElement.querySelector('.toast-body');
        if (!toastBody) {
            console.error(`Toast body not found for ${toastId}`);
            return;
        }

        toastBody.textContent = message;

        const toast = new bootstrap.Toast(toastElement);
        toast.show();
    }
}

// Define global test functions first (before app initialization)
console.log('🔧 Defining global test functions...');

window.testManualEmail = function() {
    console.log('🧪 Testing manual email functionality...');

    // Check if app exists
    if (typeof app === 'undefined' || !app) {
        console.error('❌ App not initialized yet');
        alert('App not ready yet. Please wait a moment and try again.');
        return;
    }

    app.showToast('Test toast message - function is working!', 'info');

    // Test if elements exist
    const emailInput = document.getElementById('manualEmailAddress');
    const nameInput = document.getElementById('manualContactName');
    const addBtn = document.getElementById('addManualEmailBtn');

    console.log('Elements check:');
    console.log('Email input:', emailInput ? 'Found' : 'NOT FOUND');
    console.log('Name input:', nameInput ? 'Found' : 'NOT FOUND');
    console.log('Add button:', addBtn ? 'Found' : 'NOT FOUND');

    if (emailInput && nameInput && addBtn) {
        console.log('✅ All elements found! Manual email should work.');

        // Test adding an email programmatically
        emailInput.value = '<EMAIL>';
        nameInput.value = 'Test User';
        console.log('📝 Filled in test data');

        // Try to trigger the add function
        app.handleAddManualEmail();

    } else {
        console.log('❌ Some elements missing. Check if you are on the Email Management tab.');
        alert('Elements not found. Make sure you are on the Email Management tab.');
    }
};

window.forceAddEmail = function() {
    console.log('🚀 Force adding test email...');
    if (typeof app !== 'undefined' && app) {
        app.handleAddManualEmail();
    } else {
        console.error('❌ App not initialized');
        alert('App not ready yet. Please wait a moment and try again.');
    }
};

window.checkElements = function() {
    console.log('🔍 Checking elements...');
    if (typeof app !== 'undefined' && app) {
        app.checkElementsNow();
    } else {
        console.error('❌ App not initialized');
        alert('App not ready yet. Please wait a moment and try again.');
    }
};

// Initialize the application when DOM is ready
console.log('🎯 Preparing to initialize JobSearchApp...');

// Declare app variable globally first
let app;

// Initialize when DOM is fully loaded
document.addEventListener('DOMContentLoaded', function() {
    console.log('🎯 DOM loaded, initializing JobSearchApp...');
    app = new JobSearchApp();
    window.app = app; // Make app globally accessible for debugging
    console.log('✅ JobSearchApp initialized successfully!');
});

// Debug function to test add to recipients
window.testAddToRecipients = function() {
    console.log('🧪 Testing Add to Recipients functionality');

    if (!window.app) {
        console.log('❌ App not initialized');
        return;
    }

    console.log('📊 Current app state:');
    console.log('  - foundContacts:', window.app.foundContacts);
    console.log('  - selectedRecipients:', window.app.selectedRecipients);

    if (window.app.foundContacts.length === 0) {
        console.log('⚠️ No contacts found. Please search for contacts first.');
        return;
    }

    console.log('🎯 Testing with first contact...');
    window.app.addToRecipients(0);
};

console.log('🧪 Test functions available:', {
    testManualEmail: typeof window.testManualEmail,
    forceAddEmail: typeof window.forceAddEmail,
    checkElements: typeof window.checkElements,
    testAddToRecipients: typeof window.testAddToRecipients
});

// Add some additional CSS for new elements
document.addEventListener('DOMContentLoaded', function() {
    const additionalCSS = `
        <style>
            .company-info-card { background: white; border-radius: 12px; padding: 1.5rem; }
            .info-item { display: flex; align-items: center; gap: 0.75rem; margin-bottom: 1rem; }
            .info-item i { font-size: 1.5rem; }
            .info-label { font-size: 0.8rem; color: #64748b; text-transform: uppercase; font-weight: 500; }
            .info-value { font-weight: 600; color: #1e293b; }
            .contact-preview { background: white; border-radius: 8px; padding: 1rem; border: 1px solid #e2e8f0; }
            .stat-card { background: white; border-radius: 8px; padding: 1rem; border: 1px solid #e2e8f0; }
            .stat-card .stat-number { font-size: 1.5rem; font-weight: 700; }
            .stat-card .stat-label { font-size: 0.8rem; color: #64748b; }
            .contact-info-modal { background: #f8fafc; border-radius: 8px; padding: 1rem; }
            .email-option { border: 1px solid #e2e8f0; border-radius: 8px; padding: 1rem; margin-bottom: 0.5rem; }
            .email-option:hover { background: #f8fafc; }
            .validation-result { margin-top: 0.5rem; padding-left: 1.5rem; }
            .result-item { transition: all 0.3s ease; }
            .result-item:hover { transform: translateY(-2px); box-shadow: 0 4px 12px rgba(0,0,0,0.1); }

            /* Manual Email Entry Styles */
            .email-input-options { border-bottom: 1px solid #e2e8f0; }
            .input-option-card {
                background: white;
                border: 1px solid #e2e8f0;
                border-radius: 12px;
                padding: 1.5rem;
                height: 100%;
                transition: all 0.3s ease;
            }
            .input-option-card:hover {
                border-color: #3b82f6;
                box-shadow: 0 4px 12px rgba(59, 130, 246, 0.1);
            }
            .option-header {
                display: flex;
                align-items: center;
                gap: 0.75rem;
                margin-bottom: 0.5rem;
            }
            .option-header i { font-size: 1.25rem; }
            .recipients-list { max-height: 300px; overflow-y: auto; }
            .recipient-item {
                display: flex;
                justify-content: space-between;
                align-items: center;
                padding: 0.75rem;
                border: 1px solid #e2e8f0;
                border-radius: 8px;
                margin-bottom: 0.5rem;
                background: white;
            }
            .recipient-info { flex: 1; }
            .recipient-name { font-weight: 600; color: #1e293b; }
            .recipient-email { font-size: 0.875rem; color: #64748b; font-family: monospace; }
            .empty-state {
                text-align: center;
                padding: 2rem;
                color: #64748b;
            }
            .empty-state i {
                font-size: 2rem;
                margin-bottom: 0.5rem;
                opacity: 0.5;
            }
            .manual-email-input { margin-bottom: 1rem; }
            .manual-email-list { max-height: 200px; overflow-y: auto; }
            .manual-emails-header {
                padding: 0.5rem 0;
                border-bottom: 1px solid #e2e8f0;
                margin-bottom: 0.5rem;
            }
            .manual-email-item {
                display: flex;
                justify-content: space-between;
                align-items: center;
                padding: 0.5rem;
                border: 1px solid #e2e8f0;
                border-radius: 6px;
                margin-bottom: 0.25rem;
                background: #f8fafc;
            }
            .manual-email-info { flex: 1; }
            .manual-email-name { font-weight: 500; font-size: 0.875rem; }
            .manual-email-address { font-size: 0.75rem; color: #64748b; font-family: monospace; }
            .email-config-section {
                border-top: 1px solid #e2e8f0;
                padding-top: 1.5rem;
            }
        </style>
    `;
    document.head.insertAdjacentHTML('beforeend', additionalCSS);
});